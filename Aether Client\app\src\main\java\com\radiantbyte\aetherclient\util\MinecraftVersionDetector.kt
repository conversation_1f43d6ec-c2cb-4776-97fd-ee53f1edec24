package com.radiantbyte.aetherclient.util

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build

/**
 * Utility class to detect the installed Minecraft version and its version code
 */
object MinecraftVersionDetector {

    private const val MINECRAFT_PACKAGE_NAME = "com.mojang.minecraftpe"

    /**
     * Data class to hold version information
     */
    data class MinecraftVersionInfo(
        val versionName: String,
        val versionCode: Long,
        val isInstalled: <PERSON>olean
    )

    /**
     * Detect the installed Minecraft version
     */
    fun detectMinecraftVersion(context: Context): MinecraftVersionInfo {
        return try {
            val packageManager = context.packageManager
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.getPackageInfo(
                    MINECRAFT_PACKAGE_NAME,
                    PackageManager.PackageInfoFlags.of(0)
                )
            } else {
                @Suppress("DEPRECATION")
                packageManager.getPackageInfo(MINECRAFT_PACKAGE_NAME, 0)
            }

            val versionName = packageInfo.versionName ?: "Unknown"
            val versionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.longVersionCode
            } else {
                @Suppress("DEPRECATION")
                packageInfo.versionCode.toLong()
            }

            println("MinecraftVersionDetector: Detected Minecraft version: $versionName (code: $versionCode)")

            MinecraftVersionInfo(
                versionName = versionName,
                versionCode = versionCode,
                isInstalled = true
            )
        } catch (e: PackageManager.NameNotFoundException) {
            println("MinecraftVersionDetector: Minecraft not installed")
            MinecraftVersionInfo(
                versionName = "Not Installed",
                versionCode = 0,
                isInstalled = false
            )
        } catch (e: Exception) {
            println("MinecraftVersionDetector: Error detecting Minecraft version: ${e.message}")
            MinecraftVersionInfo(
                versionName = "Error",
                versionCode = 0,
                isInstalled = false
            )
        }
    }

    /**
     * Check if Minecraft is installed
     */
    fun isMinecraftInstalled(context: Context): Boolean {
        return try {
            val packageManager = context.packageManager
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.getPackageInfo(
                    MINECRAFT_PACKAGE_NAME,
                    PackageManager.PackageInfoFlags.of(0)
                )
            } else {
                @Suppress("DEPRECATION")
                packageManager.getPackageInfo(MINECRAFT_PACKAGE_NAME, 0)
            }
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    /**
     * Get just the version code
     */
    fun getMinecraftVersionCode(context: Context): Long {
        return detectMinecraftVersion(context).versionCode
    }

    /**
     * Get just the version name
     */
    fun getMinecraftVersionName(context: Context): String {
        return detectMinecraftVersion(context).versionName
    }

    /**
     * Map version code to Minecraft version string using the versions.json mapping
     * This provides a more accurate version string than the one from PackageInfo
     */
    fun getAccurateMinecraftVersion(context: Context): String? {
        val versionCode = getMinecraftVersionCode(context)
        if (versionCode == 0L) return null

        // This would use the VersionCodeMapper from AetherProxy
        // For now, we'll return the detected version name
        return getMinecraftVersionName(context)
    }

    /**
     * Get a formatted string with version information for display
     */
    fun getVersionDisplayString(context: Context): String {
        val versionInfo = detectMinecraftVersion(context)
        return if (versionInfo.isInstalled) {
            "Minecraft ${versionInfo.versionName} (${versionInfo.versionCode})"
        } else {
            "Minecraft not installed"
        }
    }
}