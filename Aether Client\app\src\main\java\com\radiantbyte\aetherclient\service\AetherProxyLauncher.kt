package com.radiantbyte.aetherclient.service

import android.content.Context
import com.radiantbyte.aetherclient.util.MinecraftVersionDetector

/**
 * Service to launch AetherProxy with proper version detection
 */
object AetherProxyLauncher {

    /**
     * Launch AetherProxy with automatic version detection
     */
    fun launchWithVersionDetection(context: Context): Boolean {
        return try {
            val versionInfo = MinecraftVersionDetector.detectMinecraftVersion(context)

            if (!versionInfo.isInstalled) {
                println("AetherProxyLauncher: Minecraft not installed, cannot launch proxy")
                return false
            }

            println("AetherProxyLauncher: Launching AetherProxy for Minecraft ${versionInfo.versionName} (code: ${versionInfo.versionCode})")

            // Use reflection to create AetherProxy with version detection
            val aetherProxyClass = Class.forName("com.radiantbyte.aetherproxy.AetherProxy")
            val createWithVersionCodeMethod = aetherProxyClass.getMethod("createWithVersionCode", Long::class.java)
            val aetherProxy = createWithVersionCodeMethod.invoke(null, versionInfo.versionCode)

            // Set up the proxy with detected version
            val accountField = aetherProxyClass.getDeclaredField("account")
            accountField.isAccessible = true

            // Get account using reflection
            val utilClass = Class.forName("com.radiantbyte.aetherproxy.util.UtilKt")
            val fetchAccountMethod = utilClass.getMethod("fetchAccount")
            var account = fetchAccountMethod.invoke(null)

            // Check if account is expired and refresh if needed
            val isExpiredMethod = account.javaClass.getMethod("isExpired")
            val isExpired = isExpiredMethod.invoke(account) as Boolean

            if (isExpired) {
                println("AetherProxyLauncher: Account expired, refreshing...")
                val refreshMethod = utilClass.getMethod("refresh", account.javaClass)
                account = refreshMethod.invoke(null, account)

                val saveAccountMethod = utilClass.getMethod("saveAccount", account.javaClass)
                saveAccountMethod.invoke(null, account)
            }

            accountField.set(aetherProxy, account)

            // Set addresses
            val localAddressField = aetherProxyClass.getDeclaredField("localAddress")
            localAddressField.isAccessible = true
            val inetSocketAddressClass = Class.forName("java.net.InetSocketAddress")
            val localAddress = inetSocketAddressClass.getConstructor(String::class.java, Int::class.java)
                .newInstance("0.0.0.0", 19132)
            localAddressField.set(aetherProxy, localAddress)

            val remoteAddressField = aetherProxyClass.getDeclaredField("remoteAddress")
            remoteAddressField.isAccessible = true
            val remoteAddress = inetSocketAddressClass.getConstructor(String::class.java, Int::class.java)
                .newInstance("play.lbsg.net", 19132)
            remoteAddressField.set(aetherProxy, remoteAddress)

            // Initialize session and modules
            val aetherSessionField = aetherProxyClass.getDeclaredField("aetherSession")
            aetherSessionField.isAccessible = true
            val aetherSession = aetherSessionField.get(aetherProxy)

            // Set up receivers and modules using reflection
            val sessionClass = aetherSession.javaClass
            val proxyPassReceiverMethod = sessionClass.getMethod("proxyPassReceiver")
            proxyPassReceiverMethod.invoke(aetherSession)

            val definitionReceiverMethod = sessionClass.getMethod("definitionReceiver")
            definitionReceiverMethod.invoke(aetherSession)

            val transferReceiverMethod = sessionClass.getMethod("transferReceiver")
            transferReceiverMethod.invoke(aetherSession)

            val transferCommandReceiverMethod = sessionClass.getMethod("transferCommandReceiver")
            transferCommandReceiverMethod.invoke(aetherSession)

            val echoCommandReceiverMethod = sessionClass.getMethod("echoCommandReceiver")
            echoCommandReceiverMethod.invoke(aetherSession)

            // Install modules
            val moduleManagerField = sessionClass.getDeclaredField("moduleManager")
            moduleManagerField.isAccessible = true
            val moduleManager = moduleManagerField.get(aetherSession)

            val installAllModulesMethod = Class.forName("com.radiantbyte.aetherproxy.util.UtilKt")
                .getMethod("installAllModules", moduleManager.javaClass)
            installAllModulesMethod.invoke(null, moduleManager)

            // Initialize ProxyBridge with context
            ProxyBridge.setSession(aetherSession, context)

            // Boot the server
            val bootServerMethod = aetherProxyClass.getMethod("bootServer")
            bootServerMethod.invoke(aetherProxy)

            println("AetherProxyLauncher: AetherProxy launched successfully with version detection")
            true
        } catch (e: Exception) {
            println("AetherProxyLauncher: Failed to launch AetherProxy: ${e.message}")
            e.printStackTrace()
            false
        }
    }
}