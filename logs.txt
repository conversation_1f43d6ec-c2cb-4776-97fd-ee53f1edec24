25-07-28 23:20:10.064 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         I  ViewPostIme pointer 0
2025-07-28 23:20:10.096 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         I  ViewPostIme pointer 1
2025-07-28 23:20:10.098 27097-27097 System.out              com.radiantbyte.aetherclient         I  AetherClient: AetherProxy not available - no modules loaded
2025-07-28 23:20:10.099 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#addView, ty=2038, view=com.radiantbyte.aetherclient.render.RenderOverlayView{516ec94 V.ED..... ........ 0,0-0,0}, caller=android.view.WindowManagerImpl.addView:158 com.radiantbyte.aetherclient.service.AetherEngine.setupOverlay:357 com.radiantbyte.aetherclient.service.AetherEngine.start:186 
2025-07-28 23:20:10.101 27097-27097 ViewRootImpl            com.radiantbyte.aetherclient         I  dVRR is disabled
2025-07-28 23:20:10.102 27097-27123 NativeCust...ncyManager com.radiantbyte.aetherclient         D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-28 23:20:10.111 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel constructed: '7e3907b', fd=223
2025-07-28 23:20:10.111 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=Sys2038:com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 0, 0), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:10.112 27097-27097 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         I  synced displayState. AttachInfo displayState=2
2025-07-28 23:20:10.112 27097-27097 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         I  setView = com.radiantbyte.aetherclient.render.RenderOverlayView@516ec94 IsHRR=false TM=true
2025-07-28 23:20:10.113 27097-27608 System.out              com.radiantbyte.aetherclient         I  AetherGuiManager: loadGuiConfig called but config overlay system was removed
2025-07-28 23:20:10.113 27097-27608 System.out              com.radiantbyte.aetherclient         I  MinecraftVersionDetector: Detected Minecraft version: 1.21.92.1 (code: 972109201)
2025-07-28 23:20:10.113 27097-27608 AetherProxyLauncher     com.radiantbyte.aetherclient         I  Creating AetherProxy for Minecraft 1.21.92.1 (code: 972109201)
2025-07-28 23:20:10.116 27097-27097 AetherOverlay           com.radiantbyte.aetherclient         D  show() called - current state: Hidden
2025-07-28 23:20:10.117 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#addView, ty=2038, view=androidx.compose.ui.platform.ComposeView{de95cf5 V.E...... ......I. 0,0-0,0}, caller=android.view.WindowManagerImpl.addView:158 com.radiantbyte.aetherclient.overlay.AetherOverlayManager.show:61 com.radiantbyte.aetherclient.service.AetherEngine.start$lambda$0:183 
2025-07-28 23:20:10.119 27097-27097 ViewRootImpl            com.radiantbyte.aetherclient         I  dVRR is disabled
2025-07-28 23:20:10.119 27097-27123 NativeCust...ncyManager com.radiantbyte.aetherclient         D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-28 23:20:10.127 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel constructed: 'e10b957', fd=235
2025-07-28 23:20:10.128 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=Sys2038:com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 0, 0), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:10.128 27097-27097 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         I  synced displayState. AttachInfo displayState=2
2025-07-28 23:20:10.128 27097-27097 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         I  setView = androidx.compose.ui.platform.ComposeView@de95cf5 IsHRR=false TM=true
2025-07-28 23:20:10.129 27097-27097 AetherOverlay           com.radiantbyte.aetherclient         D  Button overlay shown successfully
2025-07-28 23:20:10.129 27097-27123 HWUI                    com.radiantbyte.aetherclient         D  HWUI - treat SMPTE_170M as sRGB
2025-07-28 23:20:10.136 27097-27097 Dialog                  com.radiantbyte.aetherclient         I  mIsDeviceDefault = false, mIsSamsungBasicInteraction = false, isMetaDataInActivity = false
2025-07-28 23:20:10.139 27097-27097 DecorView               com.radiantbyte.aetherclient         I  setWindowBackground: isPopOver=false color=0 d=android.graphics.drawable.ColorDrawable@1947fa9
2025-07-28 23:20:10.146 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#addView, ty=2, view=com.android.internal.policy.DecorView{42fa519 V.E...... R.....I. 0,0-0,0}[AetherActivity], caller=android.view.WindowManagerImpl.addView:158 android.app.Dialog.show:511 androidx.compose.ui.window.AndroidDialog_androidKt$Dialog$1$1.invoke:199 
2025-07-28 23:20:10.148 27097-27097 ViewRootImpl            com.radiantbyte.aetherclient         I  dVRR is disabled
2025-07-28 23:20:10.148 27097-27123 NativeCust...ncyManager com.radiantbyte.aetherclient         D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-28 23:20:10.161 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel constructed: '6e1662 ', fd=213
2025-07-28 23:20:10.162 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:10.162 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  synced displayState. AttachInfo displayState=2
2025-07-28 23:20:10.162 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  setView = com.android.internal.policy.DecorView@42fa519 IsHRR=false TM=true
2025-07-28 23:20:10.175 27097-27097 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [](id:69d900000011,api:0,p:-1,c:27097) connect: controlledByApp=false
2025-07-28 23:20:10.176 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@550033d#17](f:0,a:0,s:0) constructor()
2025-07-28 23:20:10.176 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  new BLASTBufferQueue, mName= VRI[AetherActivity]@550033d mNativeObject= 0xb4000079e58d5c00 sc.mNativeObject= 0xb400007987f63280 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-28 23:20:10.176 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  update, w= 2244 h= 1080 mName = VRI[AetherActivity]@550033d mNativeObject= 0xb4000079e58d5c00 sc.mNativeObject= 0xb400007987f63280 format= -3 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-28 23:20:10.176 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@550033d#17](f:0,a:0,s:0) update width=2244 height=1080 format=-3 mTransformHint=4
2025-07-28 23:20:10.177 27097-27097 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         I  Relayout returned: old=(96,0,2340,1080) new=(96,0,2340,1080) relayoutAsync=false req=(2244,1080)0 dur=12 res=0x3 s={true 0xb400007987fa9000} ch=true seqId=0
2025-07-28 23:20:10.178 27097-27097 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-28 23:20:10.178 27097-27097 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007987fa9000} hwInitialized=true
2025-07-28 23:20:10.179 27097-27097 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-28 23:20:10.179 27097-27097 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@550033d#50
2025-07-28 23:20:10.179 27097-27097 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@550033d#51
2025-07-28 23:20:10.180 27097-27097 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:10.180 27097-27143 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-28 23:20:10.180 27097-27143 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         I  mWNT: t=0xb400007987f7b380 mBlastBufferQueue=0xb4000079e58d5c00 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-28 23:20:10.180 27097-27143 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:10.185 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@550033d#17](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-28 23:20:10.185 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@550033d#17](f:0,a:1,s:0) acquireNextBufferLocked size=2244x1080 mFrameNumber=1 applyTransaction=true mTimestamp=221929296412689(auto) mPendingTransactions.size=0 graphicBufferId=116380728819889 transform=7
2025-07-28 23:20:10.186 27097-27123 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-28 23:20:10.186 27097-27123 HWUI                    com.radiantbyte.aetherclient         D  CFMS:: SetUp Pid : 27097    Tid : 27123
2025-07-28 23:20:10.186 27097-27097 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:10.213 27097-27097 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [](id:69d900000012,api:0,p:-1,c:27097) connect: controlledByApp=false
2025-07-28 23:20:10.214 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@12d6a8a#18](f:0,a:0,s:0) constructor()
2025-07-28 23:20:10.214 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  new BLASTBufferQueue, mName= VRI[AetherActivity]@12d6a8a mNativeObject= 0xb400007987e88400 sc.mNativeObject= 0xb400007987f63ac0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-28 23:20:10.214 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  update, w= 120 h= 120 mName = VRI[AetherActivity]@12d6a8a mNativeObject= 0xb400007987e88400 sc.mNativeObject= 0xb400007987f63ac0 format= -3 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-28 23:20:10.214 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@12d6a8a#18](f:0,a:0,s:0) update width=120 height=120 format=-3 mTransformHint=4
2025-07-28 23:20:10.214 27097-27097 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         I  Relayout returned: old=(96,200,216,320) new=(96,200,216,320) relayoutAsync=false req=(120,120)0 dur=7 res=0x3 s={true 0xb400007987fab800} ch=true seqId=0
2025-07-28 23:20:10.216 27097-27097 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-28 23:20:10.216 27097-27097 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007987fab800} hwInitialized=true
2025-07-28 23:20:10.220 27097-27097 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-28 23:20:10.220 27097-27097 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@12d6a8a#52
2025-07-28 23:20:10.220 27097-27097 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@12d6a8a#53
2025-07-28 23:20:10.221 27097-27097 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:10.223 27097-27142 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-28 23:20:10.223 27097-27142 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         I  mWNT: t=0xb400007987f7bb00 mBlastBufferQueue=0xb400007987e88400 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-28 23:20:10.223 27097-27142 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:10.224 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@12d6a8a#18](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-28 23:20:10.225 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@12d6a8a#18](f:0,a:1,s:0) acquireNextBufferLocked size=120x120 mFrameNumber=1 applyTransaction=true mTimestamp=221929335325535(auto) mPendingTransactions.size=0 graphicBufferId=116380728819899 transform=7
2025-07-28 23:20:10.225 27097-27123 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-28 23:20:10.226 27097-27123 HWUI                    com.radiantbyte.aetherclient         D  CFMS:: SetUp Pid : 27097    Tid : 27123
2025-07-28 23:20:10.226 27097-27097 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:10.256 27097-27608 System.out              com.radiantbyte.aetherclient         I  VersionMapper: Unknown Minecraft version '1.16.20.03', using default codec
2025-07-28 23:20:10.256 27097-27608 System.out              com.radiantbyte.aetherclient         I  AetherProxy: Set codec for Minecraft version 1.16.20.03 -> Protocol 819
2025-07-28 23:20:10.256 27097-27608 System.out              com.radiantbyte.aetherclient         I  AetherProxy: Set codec for version code 972109201 -> Minecraft 1.16.20.03 -> Protocol 819
2025-07-28 23:20:10.264 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: setSession called with session: com.radiantbyte.aetherproxy.session.AetherSession
2025-07-28 23:20:10.264 27097-27608 System.out              com.radiantbyte.aetherclient         I  MinecraftVersionDetector: Detected Minecraft version: 1.21.92.1 (code: 972109201)
2025-07-28 23:20:10.264 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Detected Minecraft version: 1.21.92.1 (code: 972109201)
2025-07-28 23:20:10.264 27097-27608 System.out              com.radiantbyte.aetherclient         I  VersionMapper: Unknown Minecraft version '1.16.20.03', using default codec
2025-07-28 23:20:10.264 27097-27608 System.out              com.radiantbyte.aetherclient         I  AetherProxy: Set codec for Minecraft version 1.16.20.03 -> Protocol 819
2025-07-28 23:20:10.264 27097-27608 System.out              com.radiantbyte.aetherclient         I  AetherProxy: Set codec for version code 972109201 -> Minecraft 1.16.20.03 -> Protocol 819
2025-07-28 23:20:10.264 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Successfully configured AetherProxy for Minecraft version 1.21.92.1
2025-07-28 23:20:10.264 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Setting session and refreshing modules
2025-07-28 23:20:10.264 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Refreshing modules from session: com.radiantbyte.aetherproxy.session.AetherSession
2025-07-28 23:20:10.264 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Got moduleManager: com.radiantbyte.aetherproxy.module.ModuleManager
2025-07-28 23:20:10.264 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Found 18 modules
2025-07-28 23:20:10.265 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Fly (Motion) - OFF with 0 configs
2025-07-28 23:20:10.265 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AutoSprint (Motion) - OFF with 2 configs
2025-07-28 23:20:10.265 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Spider (Motion) - OFF with 2 configs
2025-07-28 23:20:10.266 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: HighJump (Motion) - OFF with 3 configs
2025-07-28 23:20:10.266 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: MotionFly (Motion) - OFF with 3 configs
2025-07-28 23:20:10.266 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AutoWalk (Motion) - OFF with 2 configs
2025-07-28 23:20:10.266 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: BHop (Motion) - OFF with 3 configs
2025-07-28 23:20:10.266 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Jetpack (Motion) - OFF with 1 configs
2025-07-28 23:20:10.267 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Killaura (Combat) - OFF with 3 configs
2025-07-28 23:20:10.267 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Target (Combat) - ON with 2 configs
2025-07-28 23:20:10.267 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AntiKnockback (Combat) - OFF with 5 configs
2025-07-28 23:20:10.267 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Hitbox (Combat) - OFF with 4 configs
2025-07-28 23:20:10.267 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: NightVision (Effect) - OFF with 3 configs
2025-07-28 23:20:10.267 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Haste (Effect) - OFF with 3 configs
2025-07-28 23:20:10.267 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Zoom (Visual) - OFF with 2 configs
2025-07-28 23:20:10.268 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: ShowPosition (Misc) - OFF with 0 configs
2025-07-28 23:20:10.268 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Desync (Misc) - OFF with 3 configs
2025-07-28 23:20:10.268 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AntiAFK (Misc) - OFF with 2 configs
2025-07-28 23:20:10.268 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Successfully processed 18 modules
2025-07-28 23:20:10.268 27097-27608 System.out              com.radiantbyte.aetherclient         I  AetherClient: Using ProxyBridge connection (real)
2025-07-28 23:20:10.268 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Refreshing modules from session: com.radiantbyte.aetherproxy.session.AetherSession
2025-07-28 23:20:10.268 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Got moduleManager: com.radiantbyte.aetherproxy.module.ModuleManager
2025-07-28 23:20:10.268 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Found 18 modules
2025-07-28 23:20:10.268 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Fly (Motion) - OFF with 0 configs
2025-07-28 23:20:10.268 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AutoSprint (Motion) - OFF with 2 configs
2025-07-28 23:20:10.268 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Spider (Motion) - OFF with 2 configs
2025-07-28 23:20:10.268 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: HighJump (Motion) - OFF with 3 configs
2025-07-28 23:20:10.269 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: MotionFly (Motion) - OFF with 3 configs
2025-07-28 23:20:10.269 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AutoWalk (Motion) - OFF with 2 configs
2025-07-28 23:20:10.269 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: BHop (Motion) - OFF with 3 configs
2025-07-28 23:20:10.269 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Jetpack (Motion) - OFF with 1 configs
2025-07-28 23:20:10.269 27097-27097 InsetsSourceConsumer    com.radiantbyte.aetherclient         I  applyRequestedVisibilityToControl: visible=true, type=navigationBars, host=com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity
2025-07-28 23:20:10.269 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Killaura (Combat) - OFF with 3 configs
2025-07-28 23:20:10.269 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Target (Combat) - ON with 2 configs
2025-07-28 23:20:10.269 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AntiKnockback (Combat) - OFF with 5 configs
2025-07-28 23:20:10.270 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Hitbox (Combat) - OFF with 4 configs
2025-07-28 23:20:10.270 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: NightVision (Effect) - OFF with 3 configs
2025-07-28 23:20:10.270 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Haste (Effect) - OFF with 3 configs
2025-07-28 23:20:10.270 27097-27097 InsetsSourceConsumer    com.radiantbyte.aetherclient         I  applyRequestedVisibilityToControl: visible=true, type=statusBars, host=com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity
2025-07-28 23:20:10.270 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Zoom (Visual) - OFF with 2 configs
2025-07-28 23:20:10.270 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: ShowPosition (Misc) - OFF with 0 configs
2025-07-28 23:20:10.270 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Desync (Misc) - OFF with 3 configs
2025-07-28 23:20:10.270 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AntiAFK (Misc) - OFF with 2 configs
2025-07-28 23:20:10.271 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Successfully processed 18 modules
2025-07-28 23:20:10.271 27097-27608 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Session set, real connection available
2025-07-28 23:20:10.271 27097-27608 AetherEngine            com.radiantbyte.aetherclient         I  ProxyBridge session registered successfully
2025-07-28 23:20:10.271 27097-27097 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [](id:69d900000013,api:0,p:-1,c:27097) connect: controlledByApp=false
2025-07-28 23:20:10.271 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@6ed19de#19](f:0,a:0,s:0) constructor()
2025-07-28 23:20:10.272 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  new BLASTBufferQueue, mName= VRI[AetherActivity]@6ed19de mNativeObject= 0xb4000079e98f9000 sc.mNativeObject= 0xb400007987fa4040 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-28 23:20:10.272 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  update, w= 1080 h= 1164 mName = VRI[AetherActivity]@6ed19de mNativeObject= 0xb4000079e98f9000 sc.mNativeObject= 0xb400007987fa4040 format= -2 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-28 23:20:10.272 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@6ed19de#19](f:0,a:0,s:0) update width=1080 height=1164 format=-2 mTransformHint=4
2025-07-28 23:20:10.273 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  Relayout returned: old=(96,73,2205,1080) new=(700,84,1600,1068) relayoutAsync=false req=(900,984)0 dur=10 res=0x3 s={true 0xb400007987fb7800} ch=true seqId=0
2025-07-28 23:20:10.274 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-28 23:20:10.275 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007987fb7800} hwInitialized=true
2025-07-28 23:20:10.275 27097-27608 AetherEngine            com.radiantbyte.aetherclient         I  AetherProxy started at: /0.0.0.0:19132 -> play.lbsg.net/51.79.255.242:19132
2025-07-28 23:20:10.284 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-28 23:20:10.284 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@6ed19de#54
2025-07-28 23:20:10.284 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@6ed19de#55
2025-07-28 23:20:10.284 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:10.288 27097-27143 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-28 23:20:10.288 27097-27143 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  mWNT: t=0xb400007987f7bf80 mBlastBufferQueue=0xb4000079e98f9000 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-28 23:20:10.288 27097-27143 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:10.291 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@6ed19de#19](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-28 23:20:10.292 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@6ed19de#19](f:0,a:1,s:0) acquireNextBufferLocked size=1080x1164 mFrameNumber=1 applyTransaction=true mTimestamp=221929402457843(auto) mPendingTransactions.size=0 graphicBufferId=116380728819906 transform=7
2025-07-28 23:20:10.292 27097-27123 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-28 23:20:10.293 27097-27123 HWUI                    com.radiantbyte.aetherclient         D  CFMS:: SetUp Pid : 27097    Tid : 27123
2025-07-28 23:20:10.296 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:10.296 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         I  ViewPostIme pointer 0
2025-07-28 23:20:10.304 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         I  ViewPostIme pointer 1
2025-07-28 23:20:10.306 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: setSession called with session: null
2025-07-28 23:20:10.307 27097-27097 System.out              com.radiantbyte.aetherclient         I  AetherClient: AetherProxy not available - no modules loaded
2025-07-28 23:20:10.307 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Session cleared
2025-07-28 23:20:10.311 27097-27097 AetherEngine            com.radiantbyte.aetherclient         I  AetherProxy stopped
2025-07-28 23:20:10.311 27097-27097 System.out              com.radiantbyte.aetherclient         I  AetherGuiManager: saveGuiConfig called but config overlay system was removed
2025-07-28 23:20:10.312 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#removeView, ty=2038, view=com.radiantbyte.aetherclient.render.RenderOverlayView{516ec94 V.ED..... ........ 0,0-2244,1080}, caller=android.view.WindowManagerGlobal.removeView:626 android.view.WindowManagerImpl.removeView:211 com.radiantbyte.aetherclient.service.AetherEngine.stop:310 
2025-07-28 23:20:10.314 27097-27097 System.out              com.radiantbyte.aetherclient         I  AetherClient: AetherProxy not available - no modules loaded
2025-07-28 23:20:10.315 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         I  ViewPostIme pointer 0
2025-07-28 23:20:10.319 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         I  ViewPostIme pointer 1
2025-07-28 23:20:10.322 27097-27097 System.out              com.radiantbyte.aetherclient         I  AetherClient: AetherProxy not available - no modules loaded
2025-07-28 23:20:10.323 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#addView, ty=2038, view=com.radiantbyte.aetherclient.render.RenderOverlayView{90e40f8 V.ED..... ........ 0,0-0,0}, caller=android.view.WindowManagerImpl.addView:158 com.radiantbyte.aetherclient.service.AetherEngine.setupOverlay:357 com.radiantbyte.aetherclient.service.AetherEngine.start:186 
2025-07-28 23:20:10.325 27097-27097 ViewRootImpl            com.radiantbyte.aetherclient         I  dVRR is disabled
2025-07-28 23:20:10.329 27097-27123 NativeCust...ncyManager com.radiantbyte.aetherclient         D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-28 23:20:10.331 27097-27106 InputTransport          com.radiantbyte.aetherclient         D  Input channel destroyed: 'ClientS', fd=179
2025-07-28 23:20:10.331 27097-27106 InputTransport          com.radiantbyte.aetherclient         D  Input channel destroyed: 'ClientS', fd=180
2025-07-28 23:20:10.338 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel constructed: 'accdc12', fd=179
2025-07-28 23:20:10.339 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=Sys2038:com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 0, 0), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:10.340 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  synced displayState. AttachInfo displayState=2
2025-07-28 23:20:10.340 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  setView = com.radiantbyte.aetherclient.render.RenderOverlayView@90e40f8 IsHRR=false TM=true
2025-07-28 23:20:10.340 27097-27626 System.out              com.radiantbyte.aetherclient         I  AetherGuiManager: loadGuiConfig called but config overlay system was removed
2025-07-28 23:20:10.340 27097-27626 System.out              com.radiantbyte.aetherclient         I  MinecraftVersionDetector: Detected Minecraft version: 1.21.92.1 (code: 972109201)
2025-07-28 23:20:10.340 27097-27626 AetherProxyLauncher     com.radiantbyte.aetherclient         I  Creating AetherProxy for Minecraft 1.21.92.1 (code: 972109201)
2025-07-28 23:20:10.341 27097-27123 HWUI                    com.radiantbyte.aetherclient         D  HWUI - treat SMPTE_170M as sRGB
2025-07-28 23:20:10.344 27097-27626 System.out              com.radiantbyte.aetherclient         I  VersionMapper: Unknown Minecraft version '1.16.20.03', using default codec
2025-07-28 23:20:10.344 27097-27626 System.out              com.radiantbyte.aetherclient         I  AetherProxy: Set codec for Minecraft version 1.16.20.03 -> Protocol 819
2025-07-28 23:20:10.344 27097-27626 System.out              com.radiantbyte.aetherclient         I  AetherProxy: Set codec for version code 972109201 -> Minecraft 1.16.20.03 -> Protocol 819
2025-07-28 23:20:10.346 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: setSession called with session: com.radiantbyte.aetherproxy.session.AetherSession
2025-07-28 23:20:10.346 27097-27626 System.out              com.radiantbyte.aetherclient         I  MinecraftVersionDetector: Detected Minecraft version: 1.21.92.1 (code: 972109201)
2025-07-28 23:20:10.346 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Detected Minecraft version: 1.21.92.1 (code: 972109201)
2025-07-28 23:20:10.347 27097-27626 System.out              com.radiantbyte.aetherclient         I  VersionMapper: Unknown Minecraft version '1.16.20.03', using default codec
2025-07-28 23:20:10.347 27097-27626 System.out              com.radiantbyte.aetherclient         I  AetherProxy: Set codec for Minecraft version 1.16.20.03 -> Protocol 819
2025-07-28 23:20:10.347 27097-27626 System.out              com.radiantbyte.aetherclient         I  AetherProxy: Set codec for version code 972109201 -> Minecraft 1.16.20.03 -> Protocol 819
2025-07-28 23:20:10.347 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Successfully configured AetherProxy for Minecraft version 1.21.92.1
2025-07-28 23:20:10.347 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Setting session and refreshing modules
2025-07-28 23:20:10.347 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Refreshing modules from session: com.radiantbyte.aetherproxy.session.AetherSession
2025-07-28 23:20:10.347 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Got moduleManager: com.radiantbyte.aetherproxy.module.ModuleManager
2025-07-28 23:20:10.347 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Found 18 modules
2025-07-28 23:20:10.347 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Fly (Motion) - OFF with 0 configs
2025-07-28 23:20:10.347 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AutoSprint (Motion) - OFF with 2 configs
2025-07-28 23:20:10.347 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Spider (Motion) - OFF with 2 configs
2025-07-28 23:20:10.348 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: HighJump (Motion) - OFF with 3 configs
2025-07-28 23:20:10.348 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: MotionFly (Motion) - OFF with 3 configs
2025-07-28 23:20:10.348 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AutoWalk (Motion) - OFF with 2 configs
2025-07-28 23:20:10.348 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: BHop (Motion) - OFF with 3 configs
2025-07-28 23:20:10.348 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Jetpack (Motion) - OFF with 1 configs
2025-07-28 23:20:10.348 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Killaura (Combat) - OFF with 3 configs
2025-07-28 23:20:10.348 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Target (Combat) - ON with 2 configs
2025-07-28 23:20:10.348 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AntiKnockback (Combat) - OFF with 5 configs
2025-07-28 23:20:10.349 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Hitbox (Combat) - OFF with 4 configs
2025-07-28 23:20:10.349 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: NightVision (Effect) - OFF with 3 configs
2025-07-28 23:20:10.349 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Haste (Effect) - OFF with 3 configs
2025-07-28 23:20:10.349 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Zoom (Visual) - OFF with 2 configs
2025-07-28 23:20:10.349 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: ShowPosition (Misc) - OFF with 0 configs
2025-07-28 23:20:10.349 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Desync (Misc) - OFF with 3 configs
2025-07-28 23:20:10.349 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AntiAFK (Misc) - OFF with 2 configs
2025-07-28 23:20:10.349 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Successfully processed 18 modules
2025-07-28 23:20:10.349 27097-27626 System.out              com.radiantbyte.aetherclient         I  AetherClient: Using ProxyBridge connection (real)
2025-07-28 23:20:10.349 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Refreshing modules from session: com.radiantbyte.aetherproxy.session.AetherSession
2025-07-28 23:20:10.349 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Got moduleManager: com.radiantbyte.aetherproxy.module.ModuleManager
2025-07-28 23:20:10.349 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Found 18 modules
2025-07-28 23:20:10.350 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Fly (Motion) - OFF with 0 configs
2025-07-28 23:20:10.350 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AutoSprint (Motion) - OFF with 2 configs
2025-07-28 23:20:10.350 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Spider (Motion) - OFF with 2 configs
2025-07-28 23:20:10.350 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: HighJump (Motion) - OFF with 3 configs
2025-07-28 23:20:10.350 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: MotionFly (Motion) - OFF with 3 configs
2025-07-28 23:20:10.350 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AutoWalk (Motion) - OFF with 2 configs
2025-07-28 23:20:10.350 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: BHop (Motion) - OFF with 3 configs
2025-07-28 23:20:10.350 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Jetpack (Motion) - OFF with 1 configs
2025-07-28 23:20:10.351 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Killaura (Combat) - OFF with 3 configs
2025-07-28 23:20:10.351 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Target (Combat) - ON with 2 configs
2025-07-28 23:20:10.351 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AntiKnockback (Combat) - OFF with 5 configs
2025-07-28 23:20:10.351 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Hitbox (Combat) - OFF with 4 configs
2025-07-28 23:20:10.351 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: NightVision (Effect) - OFF with 3 configs
2025-07-28 23:20:10.351 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Haste (Effect) - OFF with 3 configs
2025-07-28 23:20:10.351 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Zoom (Visual) - OFF with 2 configs
2025-07-28 23:20:10.351 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: ShowPosition (Misc) - OFF with 0 configs
2025-07-28 23:20:10.351 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Desync (Misc) - OFF with 3 configs
2025-07-28 23:20:10.352 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AntiAFK (Misc) - OFF with 2 configs
2025-07-28 23:20:10.352 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Successfully processed 18 modules
2025-07-28 23:20:10.352 27097-27626 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Session set, real connection available
2025-07-28 23:20:10.352 27097-27626 AetherEngine            com.radiantbyte.aetherclient         I  ProxyBridge session registered successfully
2025-07-28 23:20:10.355 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  update, w= 1080 h= 1164 mName = VRI[AetherActivity]@6ed19de mNativeObject= 0xb4000079e98f9000 sc.mNativeObject= 0xb400007987fa4040 format= -2 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-28 23:20:10.355 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  Relayout returned: old=(700,84,1600,1068) new=(700,84,1600,1068) relayoutAsync=true req=(900,984)0 dur=0 res=0x0 s={true 0xb400007987fb7800} ch=false seqId=0
2025-07-28 23:20:10.355 27097-27626 AetherEngine            com.radiantbyte.aetherclient         I  AetherProxy started at: /0.0.0.0:19132 -> play.lbsg.net/51.79.255.242:19132
2025-07-28 23:20:10.359 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  registerCallbackForPendingTransactions
2025-07-28 23:20:10.361 27097-27142 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  mWNT: t=0xb400007add55a700 mBlastBufferQueue=0xb4000079e98f9000 fn= 2 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$9.onFrameDraw:6276 android.view.ViewRootImpl$3.onFrameDraw:2440 android.view.ThreadedRenderer$1.onFrameDraw:761 
2025-07-28 23:20:10.367 27097-27097 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [](id:69d900000014,api:0,p:-1,c:27097) connect: controlledByApp=false
2025-07-28 23:20:10.367 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@aad69d1#20](f:0,a:0,s:0) constructor()
2025-07-28 23:20:10.367 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  new BLASTBufferQueue, mName= VRI[AetherActivity]@aad69d1 mNativeObject= 0xb4000079e98f9400 sc.mNativeObject= 0xb4000079e5810ac0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-28 23:20:10.368 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  update, w= 2244 h= 1080 mName = VRI[AetherActivity]@aad69d1 mNativeObject= 0xb4000079e98f9400 sc.mNativeObject= 0xb4000079e5810ac0 format= -3 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-28 23:20:10.368 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@aad69d1#20](f:0,a:0,s:0) update width=2244 height=1080 format=-3 mTransformHint=4
2025-07-28 23:20:10.368 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  Relayout returned: old=(96,0,2340,1080) new=(96,0,2340,1080) relayoutAsync=false req=(2244,1080)0 dur=6 res=0x3 s={true 0xb400007987f4d000} ch=true seqId=0
2025-07-28 23:20:10.369 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-28 23:20:10.369 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007987f4d000} hwInitialized=true
2025-07-28 23:20:10.369 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-28 23:20:10.369 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@aad69d1#56
2025-07-28 23:20:10.370 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@aad69d1#57
2025-07-28 23:20:10.371 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:10.372 27097-27143 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-28 23:20:10.372 27097-27143 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  mWNT: t=0xb400007a21fd9980 mBlastBufferQueue=0xb4000079e98f9400 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-28 23:20:10.372 27097-27143 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:10.377 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@aad69d1#20](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-28 23:20:10.378 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@aad69d1#20](f:0,a:1,s:0) acquireNextBufferLocked size=2244x1080 mFrameNumber=1 applyTransaction=true mTimestamp=221929488448304(auto) mPendingTransactions.size=0 graphicBufferId=116380728819910 transform=7
2025-07-28 23:20:10.378 27097-27123 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-28 23:20:10.379 27097-27123 HWUI                    com.radiantbyte.aetherclient         D  CFMS:: SetUp Pid : 27097    Tid : 27123
2025-07-28 23:20:10.379 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:10.388 27097-27123 HWUI                    com.radiantbyte.aetherclient         D  HWUI - treat SMPTE_170M as sRGB
2025-07-28 23:20:10.390 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[700,84][1600,1068] display=[96,73][2205,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:10.390 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  handleResized mSyncSeqId = 0
2025-07-28 23:20:10.390 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-28 23:20:10.390 27097-27097 System.out              com.radiantbyte.aetherclient         I  AetherClient: Using ProxyBridge connection (real)
2025-07-28 23:20:10.390 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Refreshing modules from session: com.radiantbyte.aetherproxy.session.AetherSession
2025-07-28 23:20:10.390 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Got moduleManager: com.radiantbyte.aetherproxy.module.ModuleManager
2025-07-28 23:20:10.390 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Found 18 modules
2025-07-28 23:20:10.390 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Fly (Motion) - OFF with 0 configs
2025-07-28 23:20:10.391 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AutoSprint (Motion) - OFF with 2 configs
2025-07-28 23:20:10.391 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Spider (Motion) - OFF with 2 configs
2025-07-28 23:20:10.391 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: HighJump (Motion) - OFF with 3 configs
2025-07-28 23:20:10.391 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: MotionFly (Motion) - OFF with 3 configs
2025-07-28 23:20:10.391 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AutoWalk (Motion) - OFF with 2 configs
2025-07-28 23:20:10.391 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: BHop (Motion) - OFF with 3 configs
2025-07-28 23:20:10.392 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Jetpack (Motion) - OFF with 1 configs
2025-07-28 23:20:10.392 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Killaura (Combat) - OFF with 3 configs
2025-07-28 23:20:10.392 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Target (Combat) - ON with 2 configs
2025-07-28 23:20:10.392 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AntiKnockback (Combat) - OFF with 5 configs
2025-07-28 23:20:10.392 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Hitbox (Combat) - OFF with 4 configs
2025-07-28 23:20:10.392 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: NightVision (Effect) - OFF with 3 configs
2025-07-28 23:20:10.393 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Haste (Effect) - OFF with 3 configs
2025-07-28 23:20:10.393 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Zoom (Visual) - OFF with 2 configs
2025-07-28 23:20:10.393 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: ShowPosition (Misc) - OFF with 0 configs
2025-07-28 23:20:10.393 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Desync (Misc) - OFF with 3 configs
2025-07-28 23:20:10.393 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AntiAFK (Misc) - OFF with 2 configs
2025-07-28 23:20:10.394 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Successfully processed 18 modules
2025-07-28 23:20:10.394 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#removeView, ty=2038, view=androidx.compose.ui.platform.ComposeView{6148d03 V.E...... .......D 0,0-900,1007}, caller=android.view.WindowManagerGlobal.removeView:626 android.view.WindowManagerImpl.removeView:211 com.radiantbyte.aetherclient.ui.notification.NotificationManager.dismissCurrentDisconnectionNotification:343 
2025-07-28 23:20:10.396 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#addView, ty=2038, view=androidx.compose.ui.platform.ComposeView{f3a8a88 V.E...... ......I. 0,0-0,0}, caller=android.view.WindowManagerImpl.addView:158 com.radiantbyte.aetherclient.ui.notification.NotificationManager.showConnectionNotification:294 com.radiantbyte.aetherclient.service.AetherEngine.start$lambda$10$lambda$8$lambda$7$lambda$6:249 
2025-07-28 23:20:10.398 27097-27097 ViewRootImpl            com.radiantbyte.aetherclient         I  dVRR is disabled
2025-07-28 23:20:10.398 27097-27123 NativeCust...ncyManager com.radiantbyte.aetherclient         D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-28 23:20:10.406 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel constructed: '7a3480c', fd=189
2025-07-28 23:20:10.406 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=Sys2038:com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 0, 0), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:10.407 27097-27097 VRI[Aether...y]@f529c21 com.radiantbyte.aetherclient         I  synced displayState. AttachInfo displayState=2
2025-07-28 23:20:10.407 27097-27097 VRI[Aether...y]@f529c21 com.radiantbyte.aetherclient         I  setView = androidx.compose.ui.platform.ComposeView@f3a8a88 IsHRR=false TM=true
2025-07-28 23:20:10.407 27097-27097 AetherOverlay           com.radiantbyte.aetherclient         D  hide() called - current state: Button
2025-07-28 23:20:10.407 27097-27097 AetherOverlay           com.radiantbyte.aetherclient         D  Hiding overlay: AetherOverlayButton
2025-07-28 23:20:10.407 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#removeView, ty=2038, view=androidx.compose.ui.platform.ComposeView{de95cf5 V.E...... ........ 0,0-120,120}, caller=android.view.WindowManagerGlobal.removeView:626 android.view.WindowManagerImpl.removeView:211 com.radiantbyte.aetherclient.overlay.AetherOverlayManager.hide:88 
2025-07-28 23:20:10.408 27097-27097 AetherOverlay           com.radiantbyte.aetherclient         D  All overlays hidden successfully
2025-07-28 23:20:10.409 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#removeView, ty=2038, view=androidx.compose.ui.platform.ComposeView{f3a8a88 V.E...... ......I. 0,0-0,0}, caller=android.view.WindowManagerGlobal.removeView:626 android.view.WindowManagerImpl.removeView:211 com.radiantbyte.aetherclient.ui.notification.NotificationManager.dismissCurrentConnectionNotification:331 
2025-07-28 23:20:10.412 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#addView, ty=2038, view=androidx.compose.ui.platform.ComposeView{441ae59 V.E...... ......I. 0,0-0,0}, caller=android.view.WindowManagerImpl.addView:158 com.radiantbyte.aetherclient.ui.notification.NotificationManager.showDisconnectionNotification:310 com.radiantbyte.aetherclient.service.AetherEngine.stop$lambda$17:306 
2025-07-28 23:20:10.414 27097-27097 ViewRootImpl            com.radiantbyte.aetherclient         I  dVRR is disabled
2025-07-28 23:20:10.414 27097-27123 NativeCust...ncyManager com.radiantbyte.aetherclient         D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-28 23:20:10.423 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel constructed: '142d8f8', fd=204
2025-07-28 23:20:10.424 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 0, 0), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:10.424 27097-27097 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         I  synced displayState. AttachInfo displayState=2
2025-07-28 23:20:10.425 27097-27097 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         I  setView = androidx.compose.ui.platform.ComposeView@441ae59 IsHRR=false TM=true
2025-07-28 23:20:10.426 27097-27097 VRI[Aether...y]@550033d com.radiantbyte.aetherclient         I  dispatchDetachedFromWindow
2025-07-28 23:20:10.426 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@550033d#17](f:0,a:1,s:0) destructor()
2025-07-28 23:20:10.426 27097-27097 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [VRI[AetherActivity]@550033d#17(BLAST Consumer)17](id:69d900000011,api:0,p:-1,c:27097) disconnect
2025-07-28 23:20:10.430 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel destroyed: '7e3907b', fd=223
2025-07-28 23:20:10.431 27097-27097 AetherOverlay           com.radiantbyte.aetherclient         D  show() called - current state: Hidden
2025-07-28 23:20:10.432 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#addView, ty=2038, view=androidx.compose.ui.platform.ComposeView{9d1bef6 V.E...... ......I. 0,0-0,0}, caller=android.view.WindowManagerImpl.addView:158 com.radiantbyte.aetherclient.overlay.AetherOverlayManager.show:61 com.radiantbyte.aetherclient.service.AetherEngine.start$lambda$0:183 
2025-07-28 23:20:10.433 27097-27097 ViewRootImpl            com.radiantbyte.aetherclient         I  dVRR is disabled
2025-07-28 23:20:10.434 27097-27123 NativeCust...ncyManager com.radiantbyte.aetherclient         D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-28 23:20:10.444 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel constructed: '4ac07d3', fd=205
2025-07-28 23:20:10.444 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=Sys2038:com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 0, 0), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:10.444 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  synced displayState. AttachInfo displayState=2
2025-07-28 23:20:10.444 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  setView = androidx.compose.ui.platform.ComposeView@9d1bef6 IsHRR=false TM=true
2025-07-28 23:20:10.445 27097-27097 AetherOverlay           com.radiantbyte.aetherclient         D  Button overlay shown successfully
2025-07-28 23:20:10.445 27097-27123 HWUI                    com.radiantbyte.aetherclient         D  HWUI - treat SMPTE_170M as sRGB
2025-07-28 23:20:10.445 27097-27097 ImeFocusController      com.radiantbyte.aetherclient         I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-28 23:20:10.446 27097-27097 ImeFocusController      com.radiantbyte.aetherclient         I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-28 23:20:10.468 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[]@d092480#16](f:0,a:4,s:0) producer disconnected before acquireNextBufferLocked
2025-07-28 23:20:10.468 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@6ed19de#58
2025-07-28 23:20:10.468 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@6ed19de#59
2025-07-28 23:20:10.469 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:10.470 27097-27142 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=0 frameNum=3.
2025-07-28 23:20:10.470 27097-27142 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:10.474 27097-27123 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=3 didProduceBuffer=true
2025-07-28 23:20:10.474 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:10.491 27097-27097 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [](id:69d900000015,api:0,p:-1,c:27097) connect: controlledByApp=false
2025-07-28 23:20:10.491 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@f529c21#21](f:0,a:0,s:0) constructor()
2025-07-28 23:20:10.492 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  new BLASTBufferQueue, mName= VRI[AetherActivity]@f529c21 mNativeObject= 0xb4000079e58d5c00 sc.mNativeObject= 0xb4000079e9667940 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-28 23:20:10.492 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  update, w= 900 h= 1007 mName = VRI[AetherActivity]@f529c21 mNativeObject= 0xb4000079e58d5c00 sc.mNativeObject= 0xb4000079e9667940 format= -3 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-28 23:20:10.492 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@f529c21#21](f:0,a:0,s:0) update width=900 height=1007 format=-3 mTransformHint=4
2025-07-28 23:20:10.492 27097-27097 VRI[Aether...y]@f529c21 com.radiantbyte.aetherclient         I  Relayout returned: old=(96,0,2340,1080) new=(720,73,1620,1080) relayoutAsync=false req=(900,1007)0 dur=6 res=0x3 s={true 0xb400007987f4f800} ch=true seqId=0
2025-07-28 23:20:10.493 27097-27097 VRI[Aether...y]@f529c21 com.radiantbyte.aetherclient         I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-28 23:20:10.494 27097-27097 VRI[Aether...y]@f529c21 com.radiantbyte.aetherclient         D  applyTransactionOnDraw applyImmediately
2025-07-28 23:20:10.495 27097-27097 VRI[Aether...y]@f529c21 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-28 23:20:10.495 27097-27097 VRI[Aether...y]@f529c21 com.radiantbyte.aetherclient         D  applyTransactionOnDraw applyImmediately
2025-07-28 23:20:10.495 27097-27097 VRI[Aether...y]@f529c21 com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@f529c21#60
2025-07-28 23:20:10.495 27097-27097 VRI[Aether...y]@f529c21 com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@f529c21#61
2025-07-28 23:20:10.498 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@f529c21#21](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-28 23:20:10.498 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@f529c21#21](f:0,a:1,s:0) acquireNextBufferLocked size=900x1007 mFrameNumber=1 applyTransaction=true mTimestamp=221929608704920(auto) mPendingTransactions.size=0 graphicBufferId=116380728819917 transform=0
2025-07-28 23:20:10.498 27097-27097 VRI[Aether...y]@f529c21 com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:10.515 27097-27097 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [](id:69d900000016,api:0,p:-1,c:27097) connect: controlledByApp=false
2025-07-28 23:20:10.515 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[]@4ec8a1e#22](f:0,a:0,s:0) constructor()
2025-07-28 23:20:10.516 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  new BLASTBufferQueue, mName= VRI[]@4ec8a1e mNativeObject= 0xb4000079e98f9800 sc.mNativeObject= 0xb4000079e9696980 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-28 23:20:10.516 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  update, w= 900 h= 1007 mName = VRI[]@4ec8a1e mNativeObject= 0xb4000079e98f9800 sc.mNativeObject= 0xb4000079e9696980 format= -3 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-28 23:20:10.516 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[]@4ec8a1e#22](f:0,a:0,s:0) update width=900 height=1007 format=-3 mTransformHint=4
2025-07-28 23:20:10.517 27097-27097 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         I  Relayout returned: old=(96,0,2340,1080) new=(720,73,1620,1080) relayoutAsync=false req=(900,1007)0 dur=7 res=0x3 s={true 0xb400007987fa9000} ch=true seqId=0
2025-07-28 23:20:10.517 27097-27097 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-28 23:20:10.518 27097-27097 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007987fa9000} hwInitialized=true
2025-07-28 23:20:10.519 27097-27097 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-28 23:20:10.519 27097-27097 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[]@4ec8a1e#62
2025-07-28 23:20:10.519 27097-27097 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         I  Creating new active sync group VRI[]@4ec8a1e#63
2025-07-28 23:20:10.520 27097-27097 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:10.521 27097-27143 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-28 23:20:10.521 27097-27143 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         I  mWNT: t=0xb4000079e9879300 mBlastBufferQueue=0xb4000079e98f9800 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-28 23:20:10.521 27097-27143 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:10.524 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[]@4ec8a1e#22](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-28 23:20:10.524 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[]@4ec8a1e#22](f:0,a:1,s:0) acquireNextBufferLocked size=900x1007 mFrameNumber=1 applyTransaction=true mTimestamp=221929635122535(auto) mPendingTransactions.size=0 graphicBufferId=116380728819919 transform=7
2025-07-28 23:20:10.524 27097-27123 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-28 23:20:10.527 27097-27123 HWUI                    com.radiantbyte.aetherclient         D  CFMS:: SetUp Pid : 27097    Tid : 27123
2025-07-28 23:20:10.527 27097-27097 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:10.569 27097-27097 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [](id:69d900000017,api:0,p:-1,c:27097) connect: controlledByApp=false
2025-07-28 23:20:10.569 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@ec172f7#23](f:0,a:0,s:0) constructor()
2025-07-28 23:20:10.569 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  new BLASTBufferQueue, mName= VRI[AetherActivity]@ec172f7 mNativeObject= 0xb4000079e98f9c00 sc.mNativeObject= 0xb4000079e9697040 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-28 23:20:10.569 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  update, w= 120 h= 120 mName = VRI[AetherActivity]@ec172f7 mNativeObject= 0xb4000079e98f9c00 sc.mNativeObject= 0xb4000079e9697040 format= -3 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-28 23:20:10.569 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@ec172f7#23](f:0,a:0,s:0) update width=120 height=120 format=-3 mTransformHint=4
2025-07-28 23:20:10.569 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  Relayout returned: old=(96,200,216,320) new=(96,200,216,320) relayoutAsync=false req=(120,120)0 dur=5 res=0x3 s={true 0xb400007987ff3000} ch=true seqId=0
2025-07-28 23:20:10.570 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-28 23:20:10.571 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007987ff3000} hwInitialized=true
2025-07-28 23:20:10.574 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-28 23:20:10.574 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@ec172f7#64
2025-07-28 23:20:10.574 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@ec172f7#65
2025-07-28 23:20:10.575 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:10.576 27097-27142 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-28 23:20:10.576 27097-27142 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  mWNT: t=0xb4000079e987a200 mBlastBufferQueue=0xb4000079e98f9c00 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-28 23:20:10.576 27097-27142 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:10.578 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@ec172f7#23](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-28 23:20:10.579 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@ec172f7#23](f:0,a:1,s:0) acquireNextBufferLocked size=120x120 mFrameNumber=1 applyTransaction=true mTimestamp=221929689539766(auto) mPendingTransactions.size=0 graphicBufferId=116380728819930 transform=7
2025-07-28 23:20:10.579 27097-27123 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-28 23:20:10.580 27097-27123 HWUI                    com.radiantbyte.aetherclient         D  CFMS:: SetUp Pid : 27097    Tid : 27123
2025-07-28 23:20:10.581 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:10.587 27097-27097 System.out              com.radiantbyte.aetherclient         I  AetherClient: Using ProxyBridge connection (real)
2025-07-28 23:20:10.587 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Refreshing modules from session: com.radiantbyte.aetherproxy.session.AetherSession
2025-07-28 23:20:10.587 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Got moduleManager: com.radiantbyte.aetherproxy.module.ModuleManager
2025-07-28 23:20:10.587 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Found 18 modules
2025-07-28 23:20:10.587 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Fly (Motion) - OFF with 0 configs
2025-07-28 23:20:10.587 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AutoSprint (Motion) - OFF with 2 configs
2025-07-28 23:20:10.587 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Spider (Motion) - OFF with 2 configs
2025-07-28 23:20:10.587 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: HighJump (Motion) - OFF with 3 configs
2025-07-28 23:20:10.588 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: MotionFly (Motion) - OFF with 3 configs
2025-07-28 23:20:10.588 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AutoWalk (Motion) - OFF with 2 configs
2025-07-28 23:20:10.588 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: BHop (Motion) - OFF with 3 configs
2025-07-28 23:20:10.588 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Jetpack (Motion) - OFF with 1 configs
2025-07-28 23:20:10.588 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Killaura (Combat) - OFF with 3 configs
2025-07-28 23:20:10.588 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Target (Combat) - ON with 2 configs
2025-07-28 23:20:10.588 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AntiKnockback (Combat) - OFF with 5 configs
2025-07-28 23:20:10.589 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Hitbox (Combat) - OFF with 4 configs
2025-07-28 23:20:10.589 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: NightVision (Effect) - OFF with 3 configs
2025-07-28 23:20:10.589 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Haste (Effect) - OFF with 3 configs
2025-07-28 23:20:10.589 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Zoom (Visual) - OFF with 2 configs
2025-07-28 23:20:10.589 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: ShowPosition (Misc) - OFF with 0 configs
2025-07-28 23:20:10.589 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: Desync (Misc) - OFF with 3 configs
2025-07-28 23:20:10.590 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Processed module: AntiAFK (Misc) - OFF with 2 configs
2025-07-28 23:20:10.590 27097-27097 System.out              com.radiantbyte.aetherclient         I  ProxyBridge: Successfully processed 18 modules
2025-07-28 23:20:10.590 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#removeView, ty=2038, view=androidx.compose.ui.platform.ComposeView{441ae59 V.E...... ........ 0,0-900,1007}, caller=android.view.WindowManagerGlobal.removeView:626 android.view.WindowManagerImpl.removeView:211 com.radiantbyte.aetherclient.ui.notification.NotificationManager.dismissCurrentDisconnectionNotification:343 
2025-07-28 23:20:10.594 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#addView, ty=2038, view=androidx.compose.ui.platform.ComposeView{f927fb2 V.E...... ......I. 0,0-0,0}, caller=android.view.WindowManagerImpl.addView:158 com.radiantbyte.aetherclient.ui.notification.NotificationManager.showConnectionNotification:294 com.radiantbyte.aetherclient.service.AetherEngine.start$lambda$10$lambda$8$lambda$7$lambda$6:249 
2025-07-28 23:20:10.596 27097-27097 ViewRootImpl            com.radiantbyte.aetherclient         I  dVRR is disabled
2025-07-28 23:20:10.597 27097-27123 NativeCust...ncyManager com.radiantbyte.aetherclient         D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-28 23:20:10.606 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel constructed: '3de2141', fd=184
2025-07-28 23:20:10.606 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=Sys2038:com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 0, 0), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:10.606 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  synced displayState. AttachInfo displayState=2
2025-07-28 23:20:10.606 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  setView = androidx.compose.ui.platform.ComposeView@f927fb2 IsHRR=false TM=true
2025-07-28 23:20:10.607 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb400007987fb7800}
2025-07-28 23:20:10.607 27097-27097 InputMethodManagerUtils com.radiantbyte.aetherclient         D  startInputInner - Id : 0
2025-07-28 23:20:10.607 27097-27097 InputMethodManager      com.radiantbyte.aetherclient         I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-28 23:20:10.611 27097-27097 VRI[]@d092480           com.radiantbyte.aetherclient         I  dispatchDetachedFromWindow
2025-07-28 23:20:10.611 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[]@d092480#16](f:0,a:3,s:0) destructor()
2025-07-28 23:20:10.611 27097-27097 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [VRI[]@d092480#16(BLAST Consumer)16](id:69d900000010,api:0,p:-1,c:27097) disconnect
2025-07-28 23:20:10.612 27097-27112 InputTransport          com.radiantbyte.aetherclient         D  Input channel constructed: 'ClientS', fd=156
2025-07-28 23:20:10.619 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel destroyed: '981a6f ', fd=141
2025-07-28 23:20:10.623 27097-27097 VRI[Aether...y]@12d6a8a com.radiantbyte.aetherclient         I  dispatchDetachedFromWindow
2025-07-28 23:20:10.623 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@12d6a8a#18](f:0,a:1,s:0) destructor()
2025-07-28 23:20:10.623 27097-27097 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [VRI[AetherActivity]@12d6a8a#18(BLAST Consumer)18](id:69d900000012,api:0,p:-1,c:27097) disconnect
2025-07-28 23:20:10.627 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel destroyed: 'e10b957', fd=235
2025-07-28 23:20:10.630 27097-27097 VRI[Aether...y]@f529c21 com.radiantbyte.aetherclient         I  dispatchDetachedFromWindow
2025-07-28 23:20:10.630 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@f529c21#21](f:0,a:1,s:0) destructor()
2025-07-28 23:20:10.630 27097-27097 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [VRI[AetherActivity]@f529c21#21(BLAST Consumer)21](id:69d900000015,api:0,p:-1,c:27097) disconnect
2025-07-28 23:20:10.636 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel destroyed: '7a3480c', fd=189
2025-07-28 23:20:10.637 27097-27123 HWUI                    com.radiantbyte.aetherclient         D  HWUI - treat SMPTE_170M as sRGB
2025-07-28 23:20:10.700 27097-27097 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [](id:69d900000018,api:0,p:-1,c:27097) connect: controlledByApp=false
2025-07-28 23:20:10.700 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@9f4cb03#24](f:0,a:0,s:0) constructor()
2025-07-28 23:20:10.701 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  new BLASTBufferQueue, mName= VRI[AetherActivity]@9f4cb03 mNativeObject= 0xb400007a21fdc800 sc.mNativeObject= 0xb4000079e9731680 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-28 23:20:10.701 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  update, w= 900 h= 1007 mName = VRI[AetherActivity]@9f4cb03 mNativeObject= 0xb400007a21fdc800 sc.mNativeObject= 0xb4000079e9731680 format= -3 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-28 23:20:10.701 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@9f4cb03#24](f:0,a:0,s:0) update width=900 height=1007 format=-3 mTransformHint=4
2025-07-28 23:20:10.701 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  Relayout returned: old=(96,0,2340,1080) new=(720,73,1620,1080) relayoutAsync=false req=(900,1007)0 dur=25 res=0x3 s={true 0xb400007987e98800} ch=true seqId=0
2025-07-28 23:20:10.702 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-28 23:20:10.702 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007987e98800} hwInitialized=true
2025-07-28 23:20:10.704 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-28 23:20:10.704 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@9f4cb03#66
2025-07-28 23:20:10.704 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@9f4cb03#67
2025-07-28 23:20:10.706 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:10.707 27097-27143 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-28 23:20:10.707 27097-27143 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  mWNT: t=0xb4000079e987b700 mBlastBufferQueue=0xb400007a21fdc800 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-28 23:20:10.707 27097-27143 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:10.711 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@9f4cb03#24](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-28 23:20:10.711 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@9f4cb03#24](f:0,a:1,s:0) acquireNextBufferLocked size=900x1007 mFrameNumber=1 applyTransaction=true mTimestamp=221929821884074(auto) mPendingTransactions.size=0 graphicBufferId=116380728819933 transform=7
2025-07-28 23:20:10.711 27097-27123 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-28 23:20:10.712 27097-27123 HWUI                    com.radiantbyte.aetherclient         D  CFMS:: SetUp Pid : 27097    Tid : 27123
2025-07-28 23:20:10.713 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:10.717 27097-27097 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[720,73][1620,1080] display=[96,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:10.717 27097-27097 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         I  handleResized mSyncSeqId = 0
2025-07-28 23:20:10.717 27097-27097 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-28 23:20:10.722 27097-27097 VRI[]@4ec8a1e           com.radiantbyte.aetherclient         I  dispatchDetachedFromWindow
2025-07-28 23:20:10.722 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[]@4ec8a1e#22](f:0,a:1,s:0) destructor()
2025-07-28 23:20:10.723 27097-27097 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [VRI[]@4ec8a1e#22(BLAST Consumer)22](id:69d900000016,api:0,p:-1,c:27097) disconnect
2025-07-28 23:20:10.725 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel destroyed: '142d8f8', fd=204
2025-07-28 23:20:10.729 27097-27123 HWUI                    com.radiantbyte.aetherclient         D  HWUI - treat SMPTE_170M as sRGB
2025-07-28 23:20:10.730 27097-27097 InsetsSourceConsumer    com.radiantbyte.aetherclient         I  applyRequestedVisibilityToControl: visible=false, type=ime, host=com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity
2025-07-28 23:20:10.761 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[720,73][1620,1080] display=[96,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:10.761 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  handleResized mSyncSeqId = 0
2025-07-28 23:20:10.761 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-28 23:20:10.766 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@9f4cb03#68
2025-07-28 23:20:10.766 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@9f4cb03#69
2025-07-28 23:20:10.767 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:10.769 27097-27142 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=8 frameNum=3.
2025-07-28 23:20:10.769 27097-27142 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:10.778 27097-27123 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=3 didProduceBuffer=true
2025-07-28 23:20:10.778 27097-27123 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:12.289 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  ViewPostIme pointer 0
2025-07-28 23:20:12.293 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[AetherActivity]@6ed19de
2025-07-28 23:20:12.310 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  ViewPostIme pointer 1
2025-07-28 23:20:12.312 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  ViewPostIme pointer 0
2025-07-28 23:20:12.324 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#removeView, ty=2, view=com.android.internal.policy.DecorView{42fa519 V.E...... R......D 0,0-900,984 aid=1073741845}[AetherActivity], caller=android.view.WindowManagerGlobal.removeView:626 android.view.WindowManagerImpl.removeViewImmediate:216 android.app.Dialog.dismissDialog:808 
2025-07-28 23:20:12.325 27097-27097 WindowOnBackDispatcher  com.radiantbyte.aetherclient         W  sendCancelIfRunning: isInProgress=false callback=androidx.activity.OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1@73232c6
2025-07-28 23:20:12.333 27097-27123 HWUI                    com.radiantbyte.aetherclient         D  endAllActiveAnimators on 0xb400007987e5fa00 (UnprojectedRipple) with handle 0xb4000079ee46df40
2025-07-28 23:20:12.334 27097-27097 VRI[Aether...y]@6ed19de com.radiantbyte.aetherclient         I  dispatchDetachedFromWindow
2025-07-28 23:20:12.341 27097-27112 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@6ed19de#19](f:0,a:3,s:0) destructor()
2025-07-28 23:20:12.341 27097-27112 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [VRI[AetherActivity]@6ed19de#19(BLAST Consumer)19](id:69d900000013,api:0,p:-1,c:27097) disconnect
2025-07-28 23:20:12.347 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel destroyed: '6e1662 ', fd=213
2025-07-28 23:20:12.357 27097-27097 InsetsSourceConsumer    com.radiantbyte.aetherclient         I  applyRequestedVisibilityToControl: visible=true, type=navigationBars, host=com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity
2025-07-28 23:20:12.358 27097-27097 InsetsSourceConsumer    com.radiantbyte.aetherclient         I  applyRequestedVisibilityToControl: visible=true, type=statusBars, host=com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity
2025-07-28 23:20:12.360 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.handleInsetsControlChanged:2888, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:12.378 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb4000079e9919000}
2025-07-28 23:20:12.379 27097-27097 InputMethodManagerUtils com.radiantbyte.aetherclient         D  startInputInner - Id : 0
2025-07-28 23:20:12.379 27097-27097 InputMethodManager      com.radiantbyte.aetherclient         I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-28 23:20:12.384 27097-27629 InputTransport          com.radiantbyte.aetherclient         D  Input channel constructed: 'ClientS', fd=174
2025-07-28 23:20:12.410 27097-27097 InsetsSourceConsumer    com.radiantbyte.aetherclient         I  applyRequestedVisibilityToControl: visible=false, type=ime, host=com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity
2025-07-28 23:20:12.520  1598-1804  WindowManager           pid-1598                             E  win=Window{6e1662 u0 com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity EXITING} destroySurfaces: appStopped=false cleanupOnResume=false win.mWindowRemovalAllowed=true win.mRemoveOnExit=true win.mViewVisibility=0 caller=com.android.server.wm.WindowState.onExitAnimationDone:222 com.android.server.wm.WindowState.onAnimationFinished:161 com.android.server.wm.WindowContainer$$ExternalSyntheticLambda5.onAnimationFinished:26 com.android.server.wm.SurfaceAnimator$$ExternalSyntheticLambda1.run:28 com.android.server.wm.SurfaceAnimator$$ExternalSyntheticLambda0.onAnimationFinished:65 com.android.server.wm.LocalAnimationAdapter$$ExternalSyntheticLambda0.run:10 android.os.Handler.handleCallback:959 
2025-07-28 23:20:13.212 27097-27097 ImeFocusController      com.radiantbyte.aetherclient         I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-28 23:20:13.212 27097-27097 ImeFocusController      com.radiantbyte.aetherclient         I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-28 23:20:13.341 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         I  call setFrameRateCategory for touch hint category=no preference, reason=boost timeout, vri=VRI[AetherActivity]@f2ae664
2025-07-28 23:20:13.372 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel destroyed: 'ClientS', fd=150
2025-07-28 23:20:13.392 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[96,0][2340,1080] display=[-100000,-100000][100000,100000] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:13.393 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[96,200][216,320] display=[96,0][2205,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:13.393 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[720,73][1620,1080] display=[96,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:13.574 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         I  handleAppVisibility mAppVisible = true visible = false
2025-07-28 23:20:13.574 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-28 23:20:13.634 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.relayoutWindow:11304, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2340), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:13.636 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@f2ae664#6](f:0,a:3,s:0) destructor()
2025-07-28 23:20:13.636 27097-27097 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [VRI[AetherActivity]@f2ae664#6(BLAST Consumer)6](id:69d900000006,api:0,p:-1,c:27097) disconnect
2025-07-28 23:20:13.637 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)8 dur=41 res=0x2 s={false 0x0} ch=true seqId=0
2025-07-28 23:20:13.640 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         D  Not drawing due to not visible. Reason=!mAppVisible && !mForceDecorViewVisibility
2025-07-28 23:20:13.648 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=Sys2038:com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2340), mDisplayCutout=DisplayCutout{insets=Rect(0, 96 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(472, 0 - 608, 96), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(979, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(979, 2239)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 2239)}]}  mRoundedCornerFrame=Rect(0, 0 - 0, 0), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(956, 0 - 1080, 96) rotation=0}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=0 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][1080,96] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][1080,130] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][1080,96] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {27 mType=displayCutout mFrame=[0,0][1080,96] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[0,2205][1080,2340] mVisible=true mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[0,2205][1080,2340] mVisible=true mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[0,2205][1080,2340] mVisible=true mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:13.649 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[0,96][1080,2340] display=[-100000,-100000][100000,100000] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=true attachedFrameChanged=false configChanged=true displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:13.649 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  handleResized mSyncSeqId = 0
2025-07-28 23:20:13.675 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-28 23:20:13.683 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=Sys2038:com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2340), mDisplayCutout=DisplayCutout{insets=Rect(0, 96 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(472, 0 - 608, 96), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(979, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(979, 2239)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 2239)}]}  mRoundedCornerFrame=Rect(0, 0 - 0, 0), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(956, 0 - 1080, 96) rotation=0}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=0 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][1080,96] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][1080,130] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][1080,96] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {27 mType=displayCutout mFrame=[0,0][1080,96] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[0,2205][1080,2340] mVisible=true mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[0,2205][1080,2340] mVisible=true mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[0,2205][1080,2340] mVisible=true mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:13.684 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[50,200][170,320] display=[0,96][1080,2205] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=true attachedFrameChanged=false configChanged=true displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:13.684 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  handleResized mSyncSeqId = 0
2025-07-28 23:20:13.684 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-28 23:20:13.687 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=Sys2038:com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2340), mDisplayCutout=DisplayCutout{insets=Rect(0, 96 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(472, 0 - 608, 96), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(979, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(979, 2239)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 2239)}]}  mRoundedCornerFrame=Rect(0, 0 - 0, 0), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(956, 0 - 1080, 96) rotation=0}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=0 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][1080,96] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][1080,130] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][1080,96] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {27 mType=displayCutout mFrame=[0,0][1080,96] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[0,2205][1080,2340] mVisible=true mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[0,2205][1080,2340] mVisible=true mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[0,2205][1080,2340] mVisible=true mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:13.688 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[90,150][990,1157] display=[0,96][1080,2340] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=true attachedFrameChanged=false configChanged=true displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:13.688 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  handleResized mSyncSeqId = 0
2025-07-28 23:20:13.688 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-28 23:20:13.689 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         I  onDisplayChanged oldDisplayState=2 newDisplayState=2
2025-07-28 23:20:13.689 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  onDisplayChanged oldDisplayState=2 newDisplayState=2
2025-07-28 23:20:13.689 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  onDisplayChanged oldDisplayState=2 newDisplayState=2
2025-07-28 23:20:13.689 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  onDisplayChanged oldDisplayState=2 newDisplayState=2
2025-07-28 23:20:13.699 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  update, w= 1080 h= 2244 mName = VRI[AetherActivity]@aad69d1 mNativeObject= 0xb4000079e98f9400 sc.mNativeObject= 0xb4000079e5810a00 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-28 23:20:13.699 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@aad69d1#20](f:0,a:1,s:0) update width=1080 height=2244 format=-3 mTransformHint=4
2025-07-28 23:20:13.699 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  Relayout returned: old=(0,96,1080,2340) new=(0,96,1080,2340) relayoutAsync=false req=(1080,2244)0 dur=4 res=0x1 s={true 0xb400007987f4d000} ch=false seqId=0
2025-07-28 23:20:13.699 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  mThreadedRenderer.updateSurface() mSurface={isValid=true 0xb400007987f4d000}
2025-07-28 23:20:13.699 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-28 23:20:13.699 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@aad69d1#70
2025-07-28 23:20:13.699 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@aad69d1#71
2025-07-28 23:20:13.704 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:13.706 27097-27143 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=0 frameNum=2.
2025-07-28 23:20:13.706 27097-27143 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  mWNT: t=0xb400007a21fd9800 mBlastBufferQueue=0xb4000079e98f9400 fn= 2 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-28 23:20:13.707 27097-27143 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:13.711 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@aad69d1#20](f:0,a:2,s:0) producer disconnected before acquireNextBufferLocked
2025-07-28 23:20:13.711 27097-27123 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=2 didProduceBuffer=true
2025-07-28 23:20:13.712 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:13.721 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  update, w= 120 h= 120 mName = VRI[AetherActivity]@ec172f7 mNativeObject= 0xb4000079e98f9c00 sc.mNativeObject= 0xb4000079e983bf80 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-28 23:20:13.721 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  Relayout returned: old=(50,200,170,320) new=(50,200,170,320) relayoutAsync=false req=(120,120)0 dur=8 res=0x1 s={true 0xb400007987ff3000} ch=false seqId=0
2025-07-28 23:20:13.722 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  mThreadedRenderer.updateSurface() mSurface={isValid=true 0xb400007987ff3000}
2025-07-28 23:20:13.722 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-28 23:20:13.722 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@ec172f7#72
2025-07-28 23:20:13.722 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@ec172f7#73
2025-07-28 23:20:13.730 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:13.730 27097-27142 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=0 frameNum=2.
2025-07-28 23:20:13.731 27097-27142 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  mWNT: t=0xb400007987f7b080 mBlastBufferQueue=0xb4000079e98f9c00 fn= 2 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-28 23:20:13.731 27097-27142 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:13.735 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@ec172f7#23](f:0,a:2,s:0) producer disconnected before acquireNextBufferLocked
2025-07-28 23:20:13.735 27097-27123 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=2 didProduceBuffer=true
2025-07-28 23:20:13.736 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:13.746 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  update, w= 900 h= 1080 mName = VRI[AetherActivity]@9f4cb03 mNativeObject= 0xb400007a21fdc800 sc.mNativeObject= 0xb4000079e983c100 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-28 23:20:13.746 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@9f4cb03#24](f:0,a:3,s:0) update width=900 height=1080 format=-3 mTransformHint=4
2025-07-28 23:20:13.746 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  Relayout returned: old=(90,150,990,1157) new=(90,150,990,1230) relayoutAsync=false req=(900,1080)0 dur=8 res=0x1 s={true 0xb400007987e98800} ch=false seqId=0
2025-07-28 23:20:13.747 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  mThreadedRenderer.updateSurface() mSurface={isValid=true 0xb400007987e98800}
2025-07-28 23:20:13.757 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-28 23:20:13.757 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@9f4cb03#74
2025-07-28 23:20:13.757 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@9f4cb03#75
2025-07-28 23:20:13.758 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:13.758 27097-27143 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=0 frameNum=28.
2025-07-28 23:20:13.758 27097-27143 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  mWNT: t=0xb400007987f7cb80 mBlastBufferQueue=0xb400007a21fdc800 fn= 28 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-28 23:20:13.758 27097-27143 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:13.762 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@9f4cb03#24](f:0,a:4,s:0) producer disconnected before acquireNextBufferLocked
2025-07-28 23:20:13.762 27097-27123 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=28 didProduceBuffer=true
2025-07-28 23:20:13.763 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:13.769 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         I  stopped(true) old = false
2025-07-28 23:20:13.769 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         D  WindowStopped on com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity set to true
2025-07-28 23:20:13.771 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         I  onDisplayChanged oldDisplayState=2 newDisplayState=2
2025-07-28 23:20:13.771 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  onDisplayChanged oldDisplayState=2 newDisplayState=2
2025-07-28 23:20:13.771 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  onDisplayChanged oldDisplayState=2 newDisplayState=2
2025-07-28 23:20:13.771 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  onDisplayChanged oldDisplayState=2 newDisplayState=2
2025-07-28 23:20:13.782 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[90,150][990,1230] display=[0,96][1080,2340] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:13.782 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  handleResized mSyncSeqId = 0
2025-07-28 23:20:13.782 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-28 23:20:13.786 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@9f4cb03#76
2025-07-28 23:20:13.786 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@9f4cb03#77
2025-07-28 23:20:13.790  1598-5273  WindowManager           pid-1598                             E  win=Window{95a46ea u0 com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity} destroySurfaces: appStopped=true cleanupOnResume=false win.mWindowRemovalAllowed=false win.mRemoveOnExit=false win.mViewVisibility=8 caller=com.android.server.wm.ActivityRecord.destroySurfaces:25 com.android.server.wm.ActivityRecord.activityStopped:204 com.android.server.wm.ActivityClientController.activityStopped:95 android.app.IActivityClientController$Stub.onTransact:722 com.android.server.wm.ActivityClientController.onTransact:1 android.os.Binder.execTransactInternal:1541 android.os.Binder.execTransact:1480 
2025-07-28 23:20:13.800 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:13.803 27097-27142 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=0 frameNum=30.
2025-07-28 23:20:13.803 27097-27142 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:13.809 27097-27123 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=30 didProduceBuffer=true
2025-07-28 23:20:13.809 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:14.073 27097-27097 WindowManager           com.radiantbyte.aetherclient         I  WindowManagerGlobal#removeView, ty=2038, view=androidx.compose.ui.platform.ComposeView{f927fb2 V.E...... .......D 0,0-900,1080}, caller=android.view.WindowManagerGlobal.removeView:626 android.view.WindowManagerImpl.removeView:211 com.radiantbyte.aetherclient.ui.notification.NotificationManager.dismissCurrentConnectionNotification:331 
2025-07-28 23:20:14.090 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@9f4cb03#24](f:0,a:5,s:0) producer disconnected before acquireNextBufferLocked
2025-07-28 23:20:14.094 27097-27097 VRI[Aether...y]@9f4cb03 com.radiantbyte.aetherclient         I  dispatchDetachedFromWindow
2025-07-28 23:20:14.096 27097-27097 InputTransport          com.radiantbyte.aetherclient         D  Input channel destroyed: '3de2141', fd=184
2025-07-28 23:20:14.126 27097-27629 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@9f4cb03#24](f:0,a:3,s:0) destructor()
2025-07-28 23:20:14.126 27097-27629 BufferQueueConsumer     com.radiantbyte.aetherclient         D  [VRI[AetherActivity]@9f4cb03#24(BLAST Consumer)24](id:69d900000018,api:0,p:-1,c:27097) disconnect
2025-07-28 23:20:16.523 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=Sys2038:com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 0, 0), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:16.524 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[96,0][2340,1080] display=[-100000,-100000][100000,100000] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=true attachedFrameChanged=false configChanged=true displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:16.524 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  handleResized mSyncSeqId = 0
2025-07-28 23:20:16.524 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-28 23:20:16.524 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=Sys2038:com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 0, 0), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:16.524 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[96,200][216,320] display=[96,0][2205,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=true attachedFrameChanged=false configChanged=true displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:16.525 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  handleResized mSyncSeqId = 0
2025-07-28 23:20:16.525 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-28 23:20:16.539 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  update, w= 2244 h= 1080 mName = VRI[AetherActivity]@aad69d1 mNativeObject= 0xb4000079e98f9400 sc.mNativeObject= 0xb4000079e983c580 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-28 23:20:16.539 27097-27097 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@aad69d1#20](f:0,a:2,s:0) update width=2244 height=1080 format=-3 mTransformHint=4
2025-07-28 23:20:16.539 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  Relayout returned: old=(96,0,2340,1080) new=(96,0,2340,1080) relayoutAsync=false req=(2244,1080)0 dur=7 res=0x1 s={true 0xb400007987f4d000} ch=false seqId=0
2025-07-28 23:20:16.540 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  mThreadedRenderer.updateSurface() mSurface={isValid=true 0xb400007987f4d000}
2025-07-28 23:20:16.542 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-28 23:20:16.542 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@aad69d1#78
2025-07-28 23:20:16.542 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@aad69d1#79
2025-07-28 23:20:16.543 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:16.544 27097-27143 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=0 frameNum=3.
2025-07-28 23:20:16.544 27097-27143 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  mWNT: t=0xb4000079e9935300 mBlastBufferQueue=0xb4000079e98f9400 fn= 3 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-28 23:20:16.544 27097-27143 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:16.549 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@aad69d1#20](f:0,a:3,s:0) producer disconnected before acquireNextBufferLocked
2025-07-28 23:20:16.550 27097-27123 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=3 didProduceBuffer=true
2025-07-28 23:20:16.551 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:16.555 27097-27097 BLASTBufferQueue_Java   com.radiantbyte.aetherclient         I  update, w= 120 h= 120 mName = VRI[AetherActivity]@ec172f7 mNativeObject= 0xb4000079e98f9c00 sc.mNativeObject= 0xb4000079e983bf80 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-28 23:20:16.556 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  Relayout returned: old=(96,200,216,320) new=(96,200,216,320) relayoutAsync=true req=(120,120)0 dur=3 res=0x0 s={true 0xb400007987ff3000} ch=false seqId=0
2025-07-28 23:20:16.556 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  mThreadedRenderer.updateSurface() mSurface={isValid=true 0xb400007987ff3000}
2025-07-28 23:20:16.558 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  Setup new sync=wmsSync-VRI[AetherActivity]@ec172f7#80
2025-07-28 23:20:16.558 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  Creating new active sync group VRI[AetherActivity]@ec172f7#81
2025-07-28 23:20:16.560 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  registerCallbacksForSync syncBuffer=false
2025-07-28 23:20:16.562 27097-27143 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  Received frameDrawingCallback syncResult=0 frameNum=3.
2025-07-28 23:20:16.562 27097-27143 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  mWNT: t=0xb4000079e9935780 mBlastBufferQueue=0xb4000079e98f9c00 fn= 3 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-28 23:20:16.562 27097-27143 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  Setting up sync and frameCommitCallback
2025-07-28 23:20:16.570 27097-27123 BLASTBufferQueue        com.radiantbyte.aetherclient         I  [VRI[AetherActivity]@ec172f7#23](f:0,a:3,s:0) producer disconnected before acquireNextBufferLocked
2025-07-28 23:20:16.571 27097-27123 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  Received frameCommittedCallback lastAttemptedDrawFrameNum=3 didProduceBuffer=true
2025-07-28 23:20:16.571 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         D  reportDrawFinished seqId=0
2025-07-28 23:20:16.574 27097-27097 VRI[Aether...y]@f2ae664 com.radiantbyte.aetherclient         I  onDisplayChanged oldDisplayState=2 newDisplayState=2
2025-07-28 23:20:16.574 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  onDisplayChanged oldDisplayState=2 newDisplayState=2
2025-07-28 23:20:16.574 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  onDisplayChanged oldDisplayState=2 newDisplayState=2
2025-07-28 23:20:16.769 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=Sys2038:com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 0, 0), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:16.770 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[96,0][2340,1080] display=[-100000,-100000][100000,100000] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:16.771 27097-27097 InsetsController        com.radiantbyte.aetherclient         I  onStateChanged: host=Sys2038:com.radiantbyte.aetherclient/com.radiantbyte.aetherclient.app.AetherActivity, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 0, 0), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-28 23:20:16.771 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[96,200][216,320] display=[96,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:19.795 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[96,0][2340,1080] display=[-100000,-100000][100000,100000] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:19.796 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[96,200][216,320] display=[96,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:22.052 27097-27097 VRI[Aether...y]@aad69d1 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[96,0][2340,1080] display=[-100000,-100000][100000,100000] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-28 23:20:22.052 27097-27097 VRI[Aether...y]@ec172f7 com.radiantbyte.aetherclient         I  handleResized, frames=ClientWindowFrames{frame=[96,200][216,320] display=[96,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
